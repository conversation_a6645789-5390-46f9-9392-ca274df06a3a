/**
 * Responsive Messages CSS
 * Dynamic message styling for all device sizes
 */

/* ===== BASE MESSAGE STYLES ===== */

.glass-message-bubble {
  transition: all 0.2s ease-in-out;
  will-change: transform, opacity;
  contain: layout style paint;
}

.message-item {
  transition: all 0.2s ease-in-out;
}

.optimized-image {
  transition: opacity 0.2s ease;
  will-change: opacity;
}

/* ===== ULTRA-SMALL DEVICES (< 280px) ===== */
@media (max-width: 279px) {
  .glass-message-bubble {
    max-width: 85% !important;
    padding: 6px 8px !important;
    font-size: 12px !important;
    border-radius: 10px !important;
  }
  
  .message-item {
    max-width: 85% !important;
  }
  
  .glass-message-bubble img {
    max-width: 120px !important;
    max-height: 120px !important;
    border-radius: 6px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 12px !important;
    line-height: 1.3 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 9px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 20px !important;
    height: 20px !important;
  }
}

/* ===== VERY SMALL DEVICES (280px - 319px) ===== */
@media (min-width: 280px) and (max-width: 319px) {
  .glass-message-bubble {
    max-width: 87% !important;
    padding: 8px 10px !important;
    font-size: 13px !important;
    border-radius: 12px !important;
  }
  
  .message-item {
    max-width: 87% !important;
  }
  
  .glass-message-bubble img {
    max-width: 140px !important;
    max-height: 140px !important;
    border-radius: 8px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 13px !important;
    line-height: 1.35 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 10px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 24px !important;
    height: 24px !important;
  }
}

/* ===== SMALL MOBILE SCREENS (320px - 359px) ===== */
@media (min-width: 320px) and (max-width: 359px) {
  .glass-message-bubble {
    max-width: 88% !important;
    padding: 10px 12px !important;
    font-size: 14px !important;
    border-radius: 14px !important;
  }
  
  .message-item {
    max-width: 88% !important;
  }
  
  .glass-message-bubble img {
    max-width: 160px !important;
    max-height: 160px !important;
    border-radius: 10px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 14px !important;
    line-height: 1.4 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 11px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 26px !important;
    height: 26px !important;
  }
}

/* ===== STANDARD SMALL SMARTPHONES (360px - 374px) ===== */
@media (min-width: 360px) and (max-width: 374px) {
  .glass-message-bubble {
    max-width: 88% !important;
    padding: 12px 14px !important;
    font-size: 14px !important;
    border-radius: 16px !important;
  }
  
  .message-item {
    max-width: 88% !important;
  }
  
  .glass-message-bubble img {
    max-width: 180px !important;
    max-height: 180px !important;
    border-radius: 12px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 14px !important;
    line-height: 1.4 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 11px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 28px !important;
    height: 28px !important;
  }
}

/* ===== STANDARD SMARTPHONES (375px - 389px) ===== */
@media (min-width: 375px) and (max-width: 389px) {
  .glass-message-bubble {
    max-width: 90% !important;
    padding: 14px 16px !important;
    font-size: 15px !important;
    border-radius: 18px !important;
  }
  
  .message-item {
    max-width: 90% !important;
  }
  
  .glass-message-bubble img {
    max-width: 200px !important;
    max-height: 200px !important;
    border-radius: 14px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 15px !important;
    line-height: 1.45 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 12px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 30px !important;
    height: 30px !important;
  }
}

/* ===== LARGE SMARTPHONES (390px - 413px) ===== */
@media (min-width: 390px) and (max-width: 413px) {
  .glass-message-bubble {
    max-width: 90% !important;
    padding: 16px 18px !important;
    font-size: 15px !important;
    border-radius: 20px !important;
  }
  
  .message-item {
    max-width: 90% !important;
  }
  
  .glass-message-bubble img {
    max-width: 220px !important;
    max-height: 220px !important;
    border-radius: 16px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 15px !important;
    line-height: 1.45 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 12px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 32px !important;
    height: 32px !important;
  }
}

/* ===== EXTRA LARGE SMARTPHONES (414px - 427px) ===== */
@media (min-width: 414px) and (max-width: 427px) {
  .glass-message-bubble {
    max-width: 92% !important;
    padding: 18px 20px !important;
    font-size: 16px !important;
    border-radius: 22px !important;
  }
  
  .message-item {
    max-width: 92% !important;
  }
  
  .glass-message-bubble img {
    max-width: 240px !important;
    max-height: 240px !important;
    border-radius: 18px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 12px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 34px !important;
    height: 34px !important;
  }
}

/* ===== PRO SMARTPHONES (428px - 429px) ===== */
@media (min-width: 428px) and (max-width: 429px) {
  .glass-message-bubble {
    max-width: 92% !important;
    padding: 20px 22px !important;
    font-size: 16px !important;
    border-radius: 24px !important;
  }
  
  .message-item {
    max-width: 92% !important;
  }
  
  .glass-message-bubble img {
    max-width: 260px !important;
    max-height: 260px !important;
    border-radius: 20px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 13px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 36px !important;
    height: 36px !important;
  }
}

/* ===== LATEST PRO SMARTPHONES (430px - 479px) - iPhone 14 Pro Max ===== */
@media (min-width: 430px) and (max-width: 479px) {
  .glass-message-bubble {
    max-width: 93% !important;
    padding: 22px 24px !important;
    font-size: 17px !important;
    border-radius: 26px !important;
  }
  
  .message-item {
    max-width: 93% !important;
  }
  
  .glass-message-bubble img {
    max-width: 280px !important;
    max-height: 280px !important;
    border-radius: 22px !important;
  }
  
  .glass-message-bubble .chakra-text {
    font-size: 17px !important;
    line-height: 1.5 !important;
  }
  
  .glass-message-bubble .timestamp-text {
    font-size: 13px !important;
  }
  
  .glass-message-bubble .chakra-avatar {
    width: 38px !important;
    height: 38px !important;
  }
}

/* ===== LARGE MOBILE DEVICES (480px - 539px) ===== */
@media (min-width: 480px) and (max-width: 539px) {
  .glass-message-bubble {
    max-width: 94% !important;
    padding: 24px 26px !important;
    font-size: 17px !important;
    border-radius: 28px !important;
  }

  .message-item {
    max-width: 94% !important;
  }

  .glass-message-bubble img {
    max-width: 300px !important;
    max-height: 300px !important;
    border-radius: 24px !important;
  }

  .glass-message-bubble .chakra-text {
    font-size: 17px !important;
    line-height: 1.5 !important;
  }

  .glass-message-bubble .timestamp-text {
    font-size: 14px !important;
  }

  .glass-message-bubble .chakra-avatar {
    width: 40px !important;
    height: 40px !important;
  }
}

/* ===== ULTRA LARGE MOBILE DEVICES (540px - 599px) ===== */
@media (min-width: 540px) and (max-width: 599px) {
  .glass-message-bubble {
    max-width: 94% !important;
    padding: 26px 28px !important;
    font-size: 18px !important;
    border-radius: 30px !important;
  }

  .message-item {
    max-width: 94% !important;
  }

  .glass-message-bubble img {
    max-width: 320px !important;
    max-height: 320px !important;
    border-radius: 26px !important;
  }

  .glass-message-bubble .chakra-text {
    font-size: 18px !important;
    line-height: 1.5 !important;
  }

  .glass-message-bubble .timestamp-text {
    font-size: 14px !important;
  }

  .glass-message-bubble .chakra-avatar {
    width: 42px !important;
    height: 42px !important;
  }
}

/* ===== MOBILE-TABLET HYBRID DEVICES (600px - 679px) ===== */
@media (min-width: 600px) and (max-width: 679px) {
  .glass-message-bubble {
    max-width: 95% !important;
    padding: 28px 30px !important;
    font-size: 18px !important;
    border-radius: 32px !important;
  }

  .message-item {
    max-width: 95% !important;
  }

  .glass-message-bubble img {
    max-width: 340px !important;
    max-height: 340px !important;
    border-radius: 28px !important;
  }

  .glass-message-bubble .chakra-text {
    font-size: 18px !important;
    line-height: 1.5 !important;
  }

  .glass-message-bubble .timestamp-text {
    font-size: 15px !important;
  }

  .glass-message-bubble .chakra-avatar {
    width: 44px !important;
    height: 44px !important;
  }
}

/* ===== LARGE MOBILE/SMALL TABLET DEVICES (680px - 759px) ===== */
@media (min-width: 680px) and (max-width: 759px) {
  .glass-message-bubble {
    max-width: 95% !important;
    padding: 30px 32px !important;
    font-size: 19px !important;
    border-radius: 34px !important;
  }

  .message-item {
    max-width: 95% !important;
  }

  .glass-message-bubble img {
    max-width: 360px !important;
    max-height: 360px !important;
    border-radius: 30px !important;
  }

  .glass-message-bubble .chakra-text {
    font-size: 19px !important;
    line-height: 1.5 !important;
  }

  .glass-message-bubble .timestamp-text {
    font-size: 15px !important;
  }

  .glass-message-bubble .chakra-avatar {
    width: 46px !important;
    height: 46px !important;
  }
}

/* ===== SPECIFIC SUPPORT FOR 760px WIDTH DEVICES (YOUR CASE) ===== */
@media (min-width: 760px) and (max-width: 767px) {
  .glass-message-bubble {
    max-width: 96% !important;
    padding: 32px 34px !important;
    font-size: 19px !important;
    border-radius: 36px !important;
  }

  .message-item {
    max-width: 96% !important;
  }

  .glass-message-bubble img {
    max-width: 380px !important;
    max-height: 380px !important;
    border-radius: 32px !important;
  }

  .glass-message-bubble .chakra-text {
    font-size: 19px !important;
    line-height: 1.5 !important;
  }

  .glass-message-bubble .timestamp-text {
    font-size: 16px !important;
  }

  .glass-message-bubble .chakra-avatar {
    width: 48px !important;
    height: 48px !important;
  }
}

/* ===== TABLETS (768px+) ===== */
@media (min-width: 768px) and (max-width: 1023px) {
  .glass-message-bubble {
    max-width: 75% !important;
    padding: 20px 24px !important;
    font-size: 16px !important;
    border-radius: 20px !important;
  }

  .message-item {
    max-width: 75% !important;
  }

  .glass-message-bubble img {
    max-width: 300px !important;
    max-height: 300px !important;
    border-radius: 16px !important;
  }

  .glass-message-bubble .chakra-text {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  .glass-message-bubble .timestamp-text {
    font-size: 13px !important;
  }

  .glass-message-bubble .chakra-avatar {
    width: 40px !important;
    height: 40px !important;
  }
}

/* ===== DESKTOP (1024px+) ===== */
@media (min-width: 1024px) {
  .glass-message-bubble {
    max-width: 70% !important;
    padding: 22px 26px !important;
    font-size: 16px !important;
    border-radius: 22px !important;
  }

  .message-item {
    max-width: 70% !important;
  }

  .glass-message-bubble img {
    max-width: 350px !important;
    max-height: 350px !important;
    border-radius: 18px !important;
  }

  .glass-message-bubble .chakra-text {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  .glass-message-bubble .timestamp-text {
    font-size: 13px !important;
  }

  .glass-message-bubble .chakra-avatar {
    width: 42px !important;
    height: 42px !important;
  }
}

/* ===== LANDSCAPE ORIENTATION ADJUSTMENTS ===== */
@media (max-width: 767px) and (orientation: landscape) {
  .glass-message-bubble {
    max-width: calc(var(--message-max-width, 90%) - 5%) !important;
    padding: calc(var(--message-padding, 16px) * 0.8) !important;
  }

  .glass-message-bubble img {
    max-width: calc(var(--image-size, 200px) * 0.9) !important;
    max-height: calc(var(--image-size, 200px) * 0.9) !important;
  }

  .glass-message-bubble .chakra-text {
    font-size: calc(var(--text-font-size, 16px) * 0.95) !important;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
@media (max-width: 767px) {
  .glass-message-bubble,
  .message-item {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  .optimized-image {
    will-change: opacity;
    transform: translateZ(0);
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .glass-message-bubble,
  .message-item,
  .optimized-image {
    transition: none !important;
    animation: none !important;
  }
}

@media (prefers-contrast: high) {
  .glass-message-bubble {
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    background: rgba(0, 0, 0, 0.95) !important;
  }
}
