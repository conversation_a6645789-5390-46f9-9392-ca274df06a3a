/**
 * Responsive Message Hook
 * Provides dynamic message sizing and layout for all device types
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  getCurrentDeviceCategory, 
  shouldUseBottomNavigation,
  getDynamicSpacing,
  getDynamicButtonSize
} from '../utils/responsiveBreakpoints';

export const useResponsiveMessage = () => {
  const [deviceCategory, setDeviceCategory] = useState(getCurrentDeviceCategory());
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  // Update device category and window size on resize
  const updateResponsiveValues = useCallback(() => {
    const newCategory = getCurrentDeviceCategory();
    const newSize = {
      width: window.innerWidth,
      height: window.innerHeight
    };
    
    setDeviceCategory(newCategory);
    setWindowSize(newSize);
  }, []);

  // Set up event listeners
  useEffect(() => {
    const handleResize = () => {
      updateResponsiveValues();
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, [updateResponsiveValues]);

  // Get dynamic message dimensions based on device category
  const getMessageDimensions = useCallback(() => {
    const spacing = getDynamicSpacing();
    const isLandscape = windowSize.width > windowSize.height;
    const isMobile = shouldUseBottomNavigation();
    
    // Base dimensions for different device categories
    const dimensionMap = {
      xs: {
        maxWidth: { own: '85%', other: '80%' },
        maxWidthPx: { own: '280px', other: '260px' },
        padding: { text: '8px 10px', media: '6px', file: '8px' },
        fontSize: { text: '13px', timestamp: '10px', badge: '10px' },
        borderRadius: { bubble: '12px', media: '8px' },
        avatarSize: '24px',
        iconSize: '14px',
        imageSize: '140px',
        voiceHeight: '60px'
      },
      sm: {
        maxWidth: { own: '87%', other: '82%' },
        maxWidthPx: { own: '320px', other: '300px' },
        padding: { text: '10px 12px', media: '8px', file: '10px' },
        fontSize: { text: '14px', timestamp: '11px', badge: '11px' },
        borderRadius: { bubble: '14px', media: '10px' },
        avatarSize: '26px',
        iconSize: '16px',
        imageSize: '160px',
        voiceHeight: '65px'
      },
      md: {
        maxWidth: { own: '88%', other: '83%' },
        maxWidthPx: { own: '360px', other: '340px' },
        padding: { text: '12px 14px', media: '10px', file: '12px' },
        fontSize: { text: '14px', timestamp: '11px', badge: '11px' },
        borderRadius: { bubble: '16px', media: '12px' },
        avatarSize: '28px',
        iconSize: '18px',
        imageSize: '180px',
        voiceHeight: '70px'
      },
      lg: {
        maxWidth: { own: '90%', other: '85%' },
        maxWidthPx: { own: '400px', other: '380px' },
        padding: { text: '14px 16px', media: '12px', file: '14px' },
        fontSize: { text: '15px', timestamp: '12px', badge: '12px' },
        borderRadius: { bubble: '18px', media: '14px' },
        avatarSize: '30px',
        iconSize: '20px',
        imageSize: '200px',
        voiceHeight: '75px'
      },
      xl: {
        maxWidth: { own: '90%', other: '85%' },
        maxWidthPx: { own: '450px', other: '420px' },
        padding: { text: '16px 18px', media: '14px', file: '16px' },
        fontSize: { text: '15px', timestamp: '12px', badge: '12px' },
        borderRadius: { bubble: '20px', media: '16px' },
        avatarSize: '32px',
        iconSize: '22px',
        imageSize: '220px',
        voiceHeight: '80px'
      },
      '2xl': {
        maxWidth: { own: '92%', other: '87%' },
        maxWidthPx: { own: '500px', other: '470px' },
        padding: { text: '18px 20px', media: '16px', file: '18px' },
        fontSize: { text: '16px', timestamp: '12px', badge: '12px' },
        borderRadius: { bubble: '22px', media: '18px' },
        avatarSize: '34px',
        iconSize: '24px',
        imageSize: '240px',
        voiceHeight: '85px'
      },
      '3xl': {
        maxWidth: { own: '92%', other: '87%' },
        maxWidthPx: { own: '550px', other: '520px' },
        padding: { text: '20px 22px', media: '18px', file: '20px' },
        fontSize: { text: '16px', timestamp: '13px', badge: '13px' },
        borderRadius: { bubble: '24px', media: '20px' },
        avatarSize: '36px',
        iconSize: '26px',
        imageSize: '260px',
        voiceHeight: '90px'
      },
      '3xl-plus': {
        maxWidth: { own: '93%', other: '88%' },
        maxWidthPx: { own: '580px', other: '550px' },
        padding: { text: '22px 24px', media: '20px', file: '22px' },
        fontSize: { text: '17px', timestamp: '13px', badge: '13px' },
        borderRadius: { bubble: '26px', media: '22px' },
        avatarSize: '38px',
        iconSize: '28px',
        imageSize: '280px',
        voiceHeight: '95px'
      },
      '3xl-max': {
        maxWidth: { own: '94%', other: '89%' },
        maxWidthPx: { own: '620px', other: '590px' },
        padding: { text: '24px 26px', media: '22px', file: '24px' },
        fontSize: { text: '17px', timestamp: '14px', badge: '14px' },
        borderRadius: { bubble: '28px', media: '24px' },
        avatarSize: '40px',
        iconSize: '30px',
        imageSize: '300px',
        voiceHeight: '100px'
      },
      '3xl-ultra': {
        maxWidth: { own: '94%', other: '89%' },
        maxWidthPx: { own: '680px', other: '650px' },
        padding: { text: '26px 28px', media: '24px', file: '26px' },
        fontSize: { text: '18px', timestamp: '14px', badge: '14px' },
        borderRadius: { bubble: '30px', media: '26px' },
        avatarSize: '42px',
        iconSize: '32px',
        imageSize: '320px',
        voiceHeight: '105px'
      },
      '3xl-hybrid': {
        maxWidth: { own: '95%', other: '90%' },
        maxWidthPx: { own: '740px', other: '710px' },
        padding: { text: '28px 30px', media: '26px', file: '28px' },
        fontSize: { text: '18px', timestamp: '15px', badge: '15px' },
        borderRadius: { bubble: '32px', media: '28px' },
        avatarSize: '44px',
        iconSize: '34px',
        imageSize: '340px',
        voiceHeight: '110px'
      },
      '3xl-extended': {
        maxWidth: { own: '95%', other: '90%' },
        maxWidthPx: { own: '800px', other: '770px' },
        padding: { text: '30px 32px', media: '28px', file: '30px' },
        fontSize: { text: '19px', timestamp: '15px', badge: '15px' },
        borderRadius: { bubble: '34px', media: '30px' },
        avatarSize: '46px',
        iconSize: '36px',
        imageSize: '360px',
        voiceHeight: '115px'
      },
      '4xl-mobile': {
        maxWidth: { own: '96%', other: '91%' },
        maxWidthPx: { own: '860px', other: '830px' },
        padding: { text: '32px 34px', media: '30px', file: '32px' },
        fontSize: { text: '19px', timestamp: '16px', badge: '16px' },
        borderRadius: { bubble: '36px', media: '32px' },
        avatarSize: '48px',
        iconSize: '38px',
        imageSize: '380px',
        voiceHeight: '120px'
      },
      '4xl': {
        maxWidth: { own: '75%', other: '70%' },
        maxWidthPx: { own: '600px', other: '550px' },
        padding: { text: '20px 24px', media: '18px', file: '20px' },
        fontSize: { text: '16px', timestamp: '13px', badge: '13px' },
        borderRadius: { bubble: '20px', media: '16px' },
        avatarSize: '40px',
        iconSize: '24px',
        imageSize: '300px',
        voiceHeight: '85px'
      },
      '5xl': {
        maxWidth: { own: '70%', other: '65%' },
        maxWidthPx: { own: '700px', other: '650px' },
        padding: { text: '22px 26px', media: '20px', file: '22px' },
        fontSize: { text: '16px', timestamp: '13px', badge: '13px' },
        borderRadius: { bubble: '22px', media: '18px' },
        avatarSize: '42px',
        iconSize: '26px',
        imageSize: '350px',
        voiceHeight: '90px'
      }
    };

    const baseDimensions = dimensionMap[deviceCategory] || dimensionMap.lg;
    
    // Adjust for landscape orientation on mobile devices
    if (isLandscape && isMobile) {
      return {
        ...baseDimensions,
        maxWidth: {
          own: `${parseInt(baseDimensions.maxWidth.own) - 5}%`,
          other: `${parseInt(baseDimensions.maxWidth.other) - 5}%`
        },
        padding: {
          text: baseDimensions.padding.text.replace(/\d+px/g, (match) => 
            `${Math.max(8, parseInt(match) * 0.8)}px`
          ),
          media: `${Math.max(6, parseInt(baseDimensions.padding.media) * 0.8)}px`,
          file: `${Math.max(8, parseInt(baseDimensions.padding.file) * 0.8)}px`
        },
        imageSize: `${parseInt(baseDimensions.imageSize) * 0.9}px`,
        voiceHeight: `${parseInt(baseDimensions.voiceHeight) * 0.9}px`
      };
    }

    return baseDimensions;
  }, [deviceCategory, windowSize]);

  // Get responsive Chakra UI breakpoint values
  const getResponsiveValues = useCallback(() => {
    const dimensions = getMessageDimensions();
    
    return {
      // Message bubble dimensions
      maxWidth: {
        base: dimensions.maxWidthPx.own,
        '4xl': dimensions.maxWidthPx.own
      },
      maxWidthOther: {
        base: dimensions.maxWidthPx.other,
        '4xl': dimensions.maxWidthPx.other
      },
      
      // Padding values
      textPadding: {
        base: dimensions.padding.text,
        '4xl': dimensions.padding.text
      },
      mediaPadding: {
        base: dimensions.padding.media,
        '4xl': dimensions.padding.media
      },
      filePadding: {
        base: dimensions.padding.file,
        '4xl': dimensions.padding.file
      },
      
      // Font sizes
      textFontSize: {
        base: dimensions.fontSize.text,
        '4xl': dimensions.fontSize.text
      },
      timestampFontSize: {
        base: dimensions.fontSize.timestamp,
        '4xl': dimensions.fontSize.timestamp
      },
      badgeFontSize: {
        base: dimensions.fontSize.badge,
        '4xl': dimensions.fontSize.badge
      },
      
      // Border radius
      bubbleBorderRadius: {
        base: dimensions.borderRadius.bubble,
        '4xl': dimensions.borderRadius.bubble
      },
      mediaBorderRadius: {
        base: dimensions.borderRadius.media,
        '4xl': dimensions.borderRadius.media
      },
      
      // Other dimensions
      avatarSize: {
        base: dimensions.avatarSize,
        '4xl': dimensions.avatarSize
      },
      iconSize: {
        base: dimensions.iconSize,
        '4xl': dimensions.iconSize
      },
      imageSize: {
        base: dimensions.imageSize,
        '4xl': dimensions.imageSize
      },
      voiceHeight: {
        base: dimensions.voiceHeight,
        '4xl': dimensions.voiceHeight
      }
    };
  }, [getMessageDimensions]);

  return {
    // Device info
    deviceCategory,
    windowSize,
    isMobile: shouldUseBottomNavigation(),
    isLandscape: windowSize.width > windowSize.height,
    
    // Dimension functions
    getMessageDimensions,
    getResponsiveValues,
    
    // Direct access to current dimensions
    dimensions: getMessageDimensions(),
    responsive: getResponsiveValues(),
    
    // Utility functions
    updateResponsiveValues
  };
};
