import React, { useState, useEffect } from 'react';
import { Box, VStack, HStack, <PERSON>, Badge, Button, Flex, Avatar } from '@chakra-ui/react';
import { useResponsiveMessage } from '../hooks/useResponsiveMessage';
import Message from './Message';

const ResponsiveMessageTest = () => {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  const { responsive, dimensions, deviceCategory, isMobile, isLandscape } = useResponsiveMessage();

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Sample messages for testing
  const sampleMessages = [
    {
      _id: '1',
      text: 'Hello! This is a short message.',
      createdAt: new Date(),
      seen: true,
      isOptimistic: false
    },
    {
      _id: '2',
      text: 'This is a much longer message to test how the responsive system handles text wrapping and bubble sizing across different device categories. It should adapt properly to all screen sizes.',
      createdAt: new Date(),
      seen: false,
      isOptimistic: false
    },
    {
      _id: '3',
      emoji: '🎉',
      createdAt: new Date(),
      seen: true,
      isOptimistic: false
    },
    {
      _id: '4',
      text: 'Message with file attachment',
      file: '/sample-file.pdf',
      fileName: 'document.pdf',
      fileSize: 1024,
      createdAt: new Date(),
      seen: true,
      isOptimistic: false
    }
  ];

  const isYourSpecificCase = windowSize.width >= 760 && windowSize.width <= 767 && windowSize.height >= 930;

  const handleDeleteMessage = (messageId, forEveryone) => {
    console.log(`Delete message ${messageId}, for everyone: ${forEveryone}`);
  };

  return (
    <Box p={6} maxW="100%" mx="auto">
      <VStack spacing={6} align="stretch">
        <Box>
          <Text fontSize="2xl" fontWeight="bold" mb={4}>
            📱 Responsive Message Test
          </Text>
          {isYourSpecificCase && (
            <Badge colorScheme="green" fontSize="md" p={2} mb={4}>
              ✅ Your specific case detected: 760-430 x 932
            </Badge>
          )}
        </Box>

        <Box bg="gray.50" p={4} borderRadius="md" _dark={{ bg: "gray.800" }}>
          <Text fontWeight="bold" mb={3}>Device Information</Text>
          <VStack align="start" spacing={2}>
            <HStack>
              <Text fontWeight="semibold">Screen Size:</Text>
              <Text>{windowSize.width} x {windowSize.height}</Text>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Device Category:</Text>
              <Badge colorScheme="blue">{deviceCategory}</Badge>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Is Mobile:</Text>
              <Badge colorScheme={isMobile ? "green" : "red"}>
                {isMobile ? "Yes" : "No"}
              </Badge>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Is Landscape:</Text>
              <Badge colorScheme={isLandscape ? "orange" : "blue"}>
                {isLandscape ? "Yes" : "No"}
              </Badge>
            </HStack>
          </VStack>
        </Box>

        <Box bg="blue.50" p={4} borderRadius="md" _dark={{ bg: "blue.900" }}>
          <Text fontWeight="bold" mb={3}>Message Dimensions</Text>
          <VStack align="start" spacing={2}>
            <HStack>
              <Text fontWeight="semibold">Max Width (Own):</Text>
              <Text>{responsive.maxWidth.base}</Text>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Max Width (Other):</Text>
              <Text>{responsive.maxWidthOther.base}</Text>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Text Padding:</Text>
              <Text>{responsive.textPadding.base}</Text>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Text Font Size:</Text>
              <Text>{responsive.textFontSize.base}</Text>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Border Radius:</Text>
              <Text>{responsive.bubbleBorderRadius.base}</Text>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Image Size:</Text>
              <Text>{responsive.imageSize.base}</Text>
            </HStack>
            <HStack>
              <Text fontWeight="semibold">Avatar Size:</Text>
              <Text>{responsive.avatarSize.base}</Text>
            </HStack>
          </VStack>
        </Box>

        <Box bg="green.50" p={4} borderRadius="md" _dark={{ bg: "green.900" }}>
          <Text fontWeight="bold" mb={3}>Sample Messages</Text>
          <Text fontSize="sm" mb={4}>
            These messages demonstrate responsive sizing across different device categories:
          </Text>
          
          <VStack spacing={4} align="stretch">
            {/* Own messages */}
            <Box>
              <Text fontSize="sm" fontWeight="semibold" mb={2} color="blue.600">Your Messages:</Text>
              <VStack spacing={3} align="stretch">
                {sampleMessages.map((message, index) => (
                  <Message
                    key={`own-${message._id}`}
                    ownMessage={true}
                    message={message}
                    onDelete={handleDeleteMessage}
                  />
                ))}
              </VStack>
            </Box>

            {/* Other user's messages */}
            <Box>
              <Text fontSize="sm" fontWeight="semibold" mb={2} color="green.600">Other User's Messages:</Text>
              <VStack spacing={3} align="stretch">
                {sampleMessages.map((message, index) => (
                  <Message
                    key={`other-${message._id}`}
                    ownMessage={false}
                    message={{...message, _id: `other-${message._id}`}}
                    onDelete={handleDeleteMessage}
                  />
                ))}
              </VStack>
            </Box>
          </VStack>
        </Box>

        <Box bg="yellow.50" p={4} borderRadius="md" _dark={{ bg: "yellow.900" }}>
          <Text fontWeight="bold" mb={3}>Test Instructions</Text>
          <VStack align="start" spacing={2} fontSize="sm">
            <Text>1. Resize your browser window to test different breakpoints</Text>
            <Text>2. Check that messages scale appropriately for each device size</Text>
            <Text>3. Verify text remains readable at all sizes</Text>
            <Text>4. Ensure proper spacing and padding</Text>
            <Text fontWeight="bold" color="green.600">5. Pay special attention to 760px width (your specific case)!</Text>
            <Text>6. Test both portrait and landscape orientations</Text>
          </VStack>
        </Box>

        <Box bg="purple.50" p={4} borderRadius="md" _dark={{ bg: "purple.900" }}>
          <Text fontWeight="bold" mb={3}>Device Categories</Text>
          <VStack align="start" spacing={1} fontSize="sm">
            <Text>• xs (< 280px): Ultra-small devices</Text>
            <Text>• sm (280-319px): Very small smartphones</Text>
            <Text>• md (320-359px): Small smartphones</Text>
            <Text>• lg (360-374px): Standard small smartphones</Text>
            <Text>• xl (375-389px): Standard smartphones</Text>
            <Text>• 2xl (390-413px): Large smartphones</Text>
            <Text>• 3xl (414-427px): Extra large smartphones</Text>
            <Text>• 3xl-plus (428-429px): Pro smartphones</Text>
            <Text fontWeight="bold" color="purple.600">• 3xl-plus (430-479px): Latest Pro smartphones (iPhone 14 Pro Max)</Text>
            <Text>• 3xl-max (480-539px): Large mobile devices</Text>
            <Text>• 3xl-ultra (540-599px): Ultra large mobile devices</Text>
            <Text>• 3xl-hybrid (600-679px): Mobile-tablet hybrid</Text>
            <Text>• 3xl-extended (680-759px): Large mobile/small tablet</Text>
            <Text fontWeight="bold" color="green.600">• 4xl-mobile (760-767px): Your specific case!</Text>
            <Text>• 4xl (768px+): Tablets</Text>
            <Text>• 5xl (1024px+): Desktop</Text>
          </VStack>
        </Box>

        <Button 
          onClick={() => window.location.reload()} 
          colorScheme="blue" 
          size="lg"
        >
          Refresh Test
        </Button>
      </VStack>
    </Box>
  );
};

export default ResponsiveMessageTest;
